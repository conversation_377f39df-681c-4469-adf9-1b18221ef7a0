from redis import asyncio as aioredis
from redis.asyncio.connection import ConnectionPool
from config.get_config import config
from typing import Optional

# Redis配置
host = config['db_redis']['host']
db = config['db_redis']['db']
timeout = config['db_redis']['timeout']
password = config['db_redis']['password']
port = config['db_redis']['port']

# 全局Redis连接池和实例
_redis_pool: Optional[ConnectionPool] = None
_redis_instance: Optional[aioredis.Redis] = None


def create_redis_pool():
    """创建Redis连接池"""
    return ConnectionPool(
        host=host,
        port=port,
        db=db,
        password=password,
        encoding="utf-8",
        decode_responses=True,
        # 连接池配置
        max_connections=50,          # 最大连接数
        retry_on_timeout=True,       # 超时重试
        socket_keepalive=True,       # 保持连接活跃
        socket_keepalive_options={}, # 系统默认keepalive选项
        health_check_interval=30,    # 健康检查间隔(秒)
        socket_connect_timeout=5,    # 连接超时
        socket_timeout=5,            # 读写超时
    )


async def get_redis_pool():
    """获取Redis连接池"""
    global _redis_pool
    if _redis_pool is None:
        _redis_pool = create_redis_pool()
    return _redis_pool


async def get_redis():
    """获取Redis连接（作为FastAPI依赖项使用）"""
    pool = await get_redis_pool()
    redis = aioredis.Redis(connection_pool=pool)
    try:
        yield redis
    finally:
        # 连接会自动返回到池中，无需手动关闭
        pass


async def get_redis_instance():
    """获取Redis连接实例（直接调用使用）"""
    global _redis_instance
    if _redis_instance is None:
        pool = await get_redis_pool()
        _redis_instance = aioredis.Redis(connection_pool=pool)
    return _redis_instance


async def close_redis_pool():
    """关闭Redis连接池（应用关闭时调用）"""
    global _redis_pool, _redis_instance

    if _redis_instance:
        await _redis_instance.close()
        _redis_instance = None

    if _redis_pool:
        await _redis_pool.disconnect()
        _redis_pool = None


# 兼容性函数：创建独立的Redis连接（用于PubSub等长连接场景）
async def create_redis_client(**kwargs):
    """创建独立的Redis客户端（用于PubSub等特殊场景）

    Args:
        **kwargs: 额外的Redis连接参数

    Returns:
        aioredis.Redis: Redis客户端实例
    """
    default_config = {
        'host': host,
        'port': port,
        'db': db,
        'password': password,
        'encoding': "utf-8",
        'decode_responses': True,
        'retry_on_timeout': True,
        'socket_keepalive': True,
        'health_check_interval': 30,
        'socket_connect_timeout': 5,
        'socket_timeout': 5,
    }

    # 合并用户提供的配置
    default_config.update(kwargs)

    return aioredis.from_url(
        f"redis://{host}:{port}/{db}",
        password=password,
        **{k: v for k, v in default_config.items()
           if k not in ['host', 'port', 'db', 'password']}
    )
