/**
 * 聊天相关功能模块
 */
import { DEFAULT_AVATAR, MESSAGE_LIMIT, POLLING_INTERVAL, MAX_POLLING_ATTEMPTS } from './config.js';
import {
    elements,
    updateUIState,
    toggleLoadMoreButton,
    clearMessages,
    showEmptyState,
    setCurrentChatName,
    scrollToLatestMessage,
    addLoadingIndicator,
    removeLoadingIndicator
} from './dom.js';
import { getCurrentUser } from './user.js';
import { requestMessageHistory, sendChatMessage, editChatMessage, addUserToGroup } from './websocket.js';
import {
    renderChatListItem,
    updateChatItemLastMessage
} from './notification.js';
import { resetUnreadCount, handleUnreadCounts, handleOfflineMessages, getUnreadCount } from './message-status.js';
import { loadCardConfirmations } from './card-messages.js';

// =============== 状态变量 ===============
let chatrooms = [];
let tasks = []; // 存储任务数据
let currentChatroom = null;
let messages = {};
let messageOffset = {};
let initialLoadComplete = {};
let selectedMembers = [];
let collapsedTasks = new Set(); // 存储折叠的任务ID

// =============== 通用工具函数 ===============

/**
 * HTML转义
 * @param {string} text - 要转义的文本
 * @returns {string} 转义后的文本
 */
function escapeHtml(text) {
    if (!text) return '';

    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };

    return text.replace(/[&<>"']/g, m => map[m]);
}

/**
 * 格式化时间
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(date) {
    const now = new Date();
    const isToday = now.toDateString() === date.toDateString();

    if (isToday) {
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
}

/**
 * 处理图片消息内容
 * @param {string} content - 消息内容
 * @returns {string} 处理后的内容
 */
function processImageContent(content) {
    // 添加点击预览功能
    if (content.includes('<img') && !content.includes('onclick=')) {
        content = content.replace(/<img([^>]*)>/g, '<img$1 onclick="window.showImagePreview(this.src)" loading="lazy" decoding="async">');
    }

    // 处理混合内容问题 - 将HTTP资源转换为HTTPS
    if (content.includes('http://') && !content.includes('localhost')) {
        // 将所有非localhost的HTTP URL转换为HTTPS
        content = content.replace(/http:\/\/([^/]*?)\/(?!localhost)/g, 'https://$1/');

        // 特别处理360浏览器相关资源
        if (content.includes('p0.qhimg.com')) {
            content = content.replace(/http:\/\/p0\.qhimg\.com/g, 'https://p0.qhimg.com');
        }
    }

    return content;
}

// =============== 聊天室数据访问函数 ===============

/**
 * 获取当前聊天室
 * @returns {Object|null} 当前聊天室
 */
export function getCurrentChatroom() {
    return currentChatroom;
}

/**
 * 获取所有聊天室
 * @returns {Array} 聊天室数组
 */
export function getAllChatrooms() {
    return chatrooms;
}

/**
 * 获取聊天室消息
 * @param {string} chatroomId - 聊天室ID
 * @returns {Array} 消息数组
 */
export function getChatroomMessages(chatroomId) {
    return messages[chatroomId] || [];
}

/**
 * 设置聊天室列表 - 兼容新的任务数据结构
 * @param {Array|Object} data - 聊天室数组或包含任务的数据对象
 */
export function setChatrooms(data) {
    // 检查是否是新的数据结构（包含任务）
    if (data && data.records && Array.isArray(data.records)) {
        // 新的数据结构：包含任务的嵌套结构
        tasks = data.records;
        chatrooms = extractChatroomsFromTasks(tasks);
    } else if (Array.isArray(data)) {
        // 旧的数据结构：直接的聊天室数组
        chatrooms = data;
        tasks = [];
    } else {
        console.warn("无效的聊天室数据格式:", data);
        chatrooms = [];
        tasks = [];
    }

    // 按照最后消息时间排序聊天室，最新的在前面
    chatrooms = chatrooms.sort((a, b) => {
        // 如果没有最后消息，则使用聊天室创建时间
        const timeA = a.last_message ? new Date(a.last_message.created_at) : new Date(a.created_at);
        const timeB = b.last_message ? new Date(b.last_message.created_at) : new Date(b.created_at);
        return timeB - timeA; // 降序排列，最新的在前面
    });
    renderChatList();
}

/**
 * 从任务数据中提取聊天室列表
 * @param {Array} taskList - 任务列表
 * @returns {Array} 聊天室数组
 */
function extractChatroomsFromTasks(taskList) {
    const allChatrooms = [];

    taskList.forEach(task => {
        if (task.chatrooms && Array.isArray(task.chatrooms)) {
            task.chatrooms.forEach(chatroom => {
                // 为聊天室添加任务信息
                chatroom.task_info = {
                    id: task.id,
                    title: task.title,
                    admin_name: task.admin_name,
                    userid: task.userid
                };
                allChatrooms.push(chatroom);
            });
        }
    });

    return allChatrooms;
}

/**
 * 添加或更新聊天室
 * @param {Object} chatroom - 聊天室对象
 */
export function updateChatroom(chatroom) {
    const existingIndex = chatrooms.findIndex(c => c.id === chatroom.id);

    if (existingIndex !== -1) {
        chatrooms[existingIndex] = chatroom;
    } else {
        chatrooms.push(chatroom);
    }

    // 按照最后消息时间排序聊天室，最新的在前面
    chatrooms.sort((a, b) => {
        // 如果没有最后消息，则使用聊天室创建时间
        const timeA = a.last_message ? new Date(a.last_message.created_at) : new Date(a.created_at);
        const timeB = b.last_message ? new Date(b.last_message.created_at) : new Date(b.created_at);
        return timeB - timeA; // 降序排列，最新的在前面
    });

    renderChatList();
}

/**
 * 获取聊天名称
 * @param {Object} chatroom - 聊天室对象
 * @returns {string} 聊天室显示名称
 */
export function getChatName(chatroom) {
    const currentUser = getCurrentUser();
    if (!currentUser) return '未知聊天';

    if (chatroom.is_group) return chatroom.name;

    // 私聊，找出对方用户
    const otherMemberId = chatroom.members.find(id => id !== currentUser.id);
    return otherMemberId || '未知用户';
}

// =============== UI渲染函数 ===============

/**
 * 渲染聊天列表，显示所有聊天室
 * 支持按任务分组显示聊天室
 */
export function renderChatList() {
    console.log("渲染聊天列表，聊天室数量:", chatrooms.length, "任务数量:", tasks.length);
    elements.chatList.innerHTML = '';

    if (tasks.length > 0) {
        // 新的数据结构：按任务分组显示
        renderChatListByTasks();
    } else {
        // 旧的数据结构：直接显示聊天室列表
        renderChatListFlat();
    }

    // 不再自动选择第一个聊天室，让用户手动选择
    if (!currentChatroom && chatrooms.length > 0) {
        console.log("有可用的聊天室，但不自动选择，等待用户点击");
        // 显示空状态提示
        showEmptyState('请从左侧选择一个聊天');
    }
}

/**
 * 按任务分组渲染聊天列表
 */
function renderChatListByTasks() {
    tasks.forEach(task => {
        // 始终创建任务标题，无论是否有聊天室
        const taskHeader = createTaskHeader(task);
        elements.chatList.appendChild(taskHeader);

        // 检查任务是否折叠
        const isCollapsed = collapsedTasks.has(task.id);

        // 如果任务未折叠且有聊天室，渲染聊天室
        if (!isCollapsed && task.chatrooms && task.chatrooms.length > 0) {
            task.chatrooms.forEach(chatroom => {
                const displayName = chatroom.is_group ? chatroom.name : getChatName(chatroom);
                const isActive = currentChatroom && currentChatroom.id === chatroom.id;

                // 从message-status.js获取未读计数
                const unreadCount = getUnreadCount(chatroom.id);
                const chatItem = renderChatListItem(
                    chatroom,
                    displayName,
                    isActive,
                    selectedChatroom => selectChatroom(selectedChatroom),
                    unreadCount
                );

                // 为聊天室项添加缩进样式
                chatItem.classList.add('chat-item-indented');
                elements.chatList.appendChild(chatItem);
            });
        }
    });
}

/**
 * 扁平化渲染聊天列表（兼容旧数据结构）
 */
function renderChatListFlat() {
    chatrooms.forEach(chatroom => {
        const displayName = chatroom.is_group ? chatroom.name : getChatName(chatroom);
        const isActive = currentChatroom && currentChatroom.id === chatroom.id;

        // 从message-status.js获取未读计数
        const unreadCount = getUnreadCount(chatroom.id);
        const chatItem = renderChatListItem(
            chatroom,
            displayName,
            isActive,
            selectedChatroom => selectChatroom(selectedChatroom),
            unreadCount
        );
        elements.chatList.appendChild(chatItem);
    });
}

/**
 * 创建任务标题元素
 * @param {Object} task - 任务对象
 * @returns {HTMLElement} 任务标题元素
 */
function createTaskHeader(task) {
    const taskHeader = document.createElement('div');
    taskHeader.className = 'task-header';
    taskHeader.dataset.taskId = task.id;

    const isCollapsed = collapsedTasks.has(task.id);
    const chatroomCount = task.chatrooms ? task.chatrooms.length : 0;

    taskHeader.innerHTML = `
        <div class="task-header-content">
            <div class="task-collapse-icon ${isCollapsed ? 'collapsed' : ''}">${isCollapsed ? '▶' : '▼'}</div>
            <div class="task-title">${task.title}</div>
        </div>
        <div class="task-info">
            <span class="task-admin">管理员: ${task.admin_name}</span>
            <span class="task-chat-count">${chatroomCount}个聊天室</span>
        </div>
    `;

    // 添加点击事件处理折叠展开
    taskHeader.addEventListener('click', () => {
        toggleTaskCollapse(task.id);
    });

    return taskHeader;
}

/**
 * 切换任务折叠状态
 * @param {number} taskId - 任务ID
 */
function toggleTaskCollapse(taskId) {
    if (collapsedTasks.has(taskId)) {
        collapsedTasks.delete(taskId);
    } else {
        collapsedTasks.add(taskId);
    }

    // 重新渲染聊天列表
    renderChatList();
}

/**
 * 渲染消息
 */
export function renderMessages(isLoadingMore = false) {
    if (!currentChatroom || !messages[currentChatroom.id]) {
        console.warn("尝试渲染消息但没有当前聊天室或消息");
        return;
    }

    console.log("渲染消息，数量:", messages[currentChatroom.id].length);

    // 先确保消息按时间排序 - 从旧到新（显示时从上到下）
    messages[currentChatroom.id].sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

    // 清空消息区域
    clearMessages();

    // 检查是否有消息
    if (messages[currentChatroom.id].length === 0) {
        showEmptyState('暂无消息，开始聊天吧');
        return;
    }

    // 切换聊天消息容器的显示方向
    elements.chatMessages.style.flexDirection = 'column';

    // 检测浏览器类型
    const userAgent = navigator.userAgent;
    const is360Browser = userAgent.indexOf('QIHU') > -1 || userAgent.indexOf('360Browser') > -1 || userAgent.indexOf('QHBrowser') > -1;

    // 渲染所有消息
    messages[currentChatroom.id].forEach(msg => {
        const messageElement = createMessageElement(msg);
        elements.chatMessages.appendChild(messageElement);
    });

    // 基本的重绘触发 - 对所有浏览器都适用且影响最小
    void elements.chatMessages.offsetHeight;

    // 针对360浏览器的特殊处理
    if (is360Browser) {
        console.log("渲染消息 - 检测到360浏览器，应用特殊渲染逻辑");

        // 使用多种方式强制重绘
        elements.chatMessages.style.opacity = '0.99';
        void elements.chatMessages.offsetHeight;
        elements.chatMessages.style.opacity = '1';

        // 使用transform触发GPU加速渲染
        elements.chatMessages.style.transform = 'translateZ(0)';
        void elements.chatMessages.offsetHeight;

        // 使用classList添加和移除类来触发重绘
        elements.chatMessages.classList.add('force-redraw');
        setTimeout(() => {
            // 强制重绘方法：显示/隐藏
            elements.chatMessages.style.display = 'none';
            void elements.chatMessages.offsetHeight;
            elements.chatMessages.style.display = 'flex';

            elements.chatMessages.classList.remove('force-redraw');
        }, 50);
    }

    // 只有在不是加载更多消息的情况下才滚动到最新消息
    if (!isLoadingMore) {
        // 使用requestAnimationFrame确保在下一帧渲染
        requestAnimationFrame(() => {
            scrollToLatestMessage();

            // 针对360浏览器，再次触发重绘
            if (is360Browser) {
                void elements.chatMessages.offsetHeight;
            }
        });
    }

    // 重置未读计数，因为用户正在查看这个聊天室
    resetUnreadCount(currentChatroom.id);
}

/**
 * 创建消息元素
 * @param {Object} msg - 消息对象
 * @returns {HTMLElement} 消息元素
 */
export function createMessageElement(msg) {
    const currentUser = getCurrentUser();
    const isCurrentUser = msg.sender_id === currentUser.id;
    const messageElement = document.createElement('div');

    // 系统消息的特殊处理
    if (msg.sender_id === 'system') {
        messageElement.className = 'message-system';
        messageElement.textContent = msg.content;
        return messageElement;
    }

    const formattedTime = formatTime(new Date(msg.created_at));

    // 准备消息内容
    let messageContent = msg.is_html ? msg.content : escapeHtml(msg.content);

    // 处理图片消息
    if (msg.is_html) {
        messageContent = processImageContent(messageContent);
    }

    // 检查是否是卡片消息
    const isCardMessage = msg.content_type === 'card' || (msg.message_type && msg.message_type.startsWith('card_'));

    // 根据是群聊还是私聊，以及是否是当前用户消息，应用不同的样式
    if (currentChatroom.is_group) {
        // 群聊消息样式
        messageElement.className = `message message-group ${isCurrentUser ? 'message-current-user' : ''} ${isCardMessage ? 'card-message' : ''}`;
        messageElement.innerHTML = `
            <div class="message-sender-avatar">
                <img src="${DEFAULT_AVATAR}" alt="头像">
            </div>
            <div class="message-bubble">
                <div class="message-sender-name">${msg.sender_name || msg.sender_id}</div>
                <div class="message-content">${messageContent}</div>
                <div class="message-info">
                    <span class="message-time">${formattedTime}</span>
                    ${msg.is_edited ? '<span class="message-edited">(已编辑)</span>' : ''}
                </div>
            </div>
        `;
    } else {
        // 私聊消息样式
        messageElement.className = `message ${isCurrentUser ? 'message-sent' : 'message-received'} ${isCardMessage ? 'card-message' : ''}`;
        messageElement.innerHTML = `
            <div class="message-content">${messageContent}</div>
            <div class="message-info">
                <span class="message-time">${formattedTime}</span>
                ${msg.is_edited ? '<span class="message-edited">(已编辑)</span>' : ''}
            </div>
        `;
    }

    // 如果是当前用户发送的消息，添加编辑功能
    if (isCurrentUser) {
        const messageContent = messageElement.querySelector('.message-content');
        messageContent.addEventListener('dblclick', () => {
            const newContent = prompt('编辑消息:', msg.content);
            if (newContent && newContent !== msg.content) {
                editMessage(msg.id, newContent);
            }
        });
    }

    return messageElement;
}

/**
 * 显示当前聊天信息
 * @param {string} chatId - 聊天室ID
 */
export function displayCurrentChatInfo(chatId) {
    try {
        const chat = chatrooms.find(c => c.id === chatId);
        if (!chat) {
            console.warn("找不到聊天室:", chatId);
            return;
        }

        // 设置基本名称
        if (chat.is_group) {
            setCurrentChatName(chat.name);
            displayGroupMembers(chat);
        } else {
            // 私聊处理
            const otherMemberId = chat.members.find(id => id !== getCurrentUser().id);
            setCurrentChatName(otherMemberId || "未知用户");

            // 对于私聊，隐藏成员面板
            if (elements.groupMembersPanel) {
                elements.groupMembersPanel.style.display = 'none';
            }
        }
    } catch (error) {
        console.error("显示聊天信息时出错:", error);
    }
}

/**
 * 显示群组成员信息
 * @param {Object} chat - 聊天室对象
 */
function displayGroupMembers(chat) {
    if (!chat.is_group) return;

    // 显示成员面板
    if (elements.groupMembersPanel) {
        elements.groupMembersPanel.style.display = 'flex';
    }

    // 获取成员数据（支持新旧数据结构）
    const members = getMembersData(chat);
    const countElement = document.createElement('span');
    countElement.className = 'group-members-count';
    countElement.textContent = `(${members.length}人)`;

    // 清除旧的成员计数
    const oldCount = elements.currentChatName.querySelector('.group-members-count');
    if (oldCount) oldCount.remove();

    elements.currentChatName.appendChild(countElement);

    // 更新右侧成员列表
    if (elements.membersList) {
        elements.membersList.innerHTML = '';
        members.forEach(member => {
            const memberItem = document.createElement('div');
            memberItem.className = 'member-item';

            // 根据数据结构显示成员信息
            if (typeof member === 'object' && member.user_id) {
                // 新的数据结构：包含用户类型信息
                memberItem.innerHTML = `
                    <img src="${DEFAULT_AVATAR}" alt="头像" class="member-avatar">
                    <div class="member-info">
                        <div class="member-name">${member.user_id}</div>
                        <div class="member-type">${member.user_type_name || '成员'}</div>
                    </div>
                `;
            } else {
                // 旧的数据结构：只有用户ID
                memberItem.innerHTML = `
                    <img src="${DEFAULT_AVATAR}" alt="头像" class="member-avatar">
                    <div class="member-name">${member}</div>
                `;
            }

            elements.membersList.appendChild(memberItem);
        });
    }
}

/**
 * 获取成员数据，兼容新旧数据结构
 * @param {Object} chat - 聊天室对象
 * @returns {Array} 成员数组
 */
function getMembersData(chat) {
    if (!chat.members) return [];

    // 检查是否是新的数据结构（包含用户类型信息）
    if (Array.isArray(chat.members) && chat.members.length > 0) {
        const firstMember = chat.members[0];
        if (typeof firstMember === 'object' && firstMember.user_id) {
            // 新的数据结构
            return chat.members;
        }
    }

    // 旧的数据结构：直接返回用户ID数组
    return chat.members;
}

/**
 * 更新选中的成员UI
 */
export function updateSelectedMembers() {
    if (!elements.selectedMembersEl) return;

    elements.selectedMembersEl.innerHTML = '';

    selectedMembers.forEach(member => {
        const memberElement = document.createElement('div');
        memberElement.className = 'selected-member';
        memberElement.innerHTML = `
            <img src="${member.avatar || DEFAULT_AVATAR}" alt="头像">
            <span>${member.username}</span>
            <span class="remove-member" data-id="${member.id}">×</span>
        `;

        elements.selectedMembersEl.appendChild(memberElement);
    });

    // 绑定移除成员事件
    document.querySelectorAll('.remove-member').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const memberId = e.target.getAttribute('data-id');
            removeSelectedMember(memberId);
        });
    });
}

// =============== 聊天室操作函数 ===============

/**
 * 选择聊天室
 * @param {Object} chatroom - 聊天室对象
 */
export function selectChatroom(chatroom) {
    if (!chatroom) {
        console.error("尝试选择无效的聊天室");
        return;
    }

    console.log("选择聊天室:", chatroom.id, chatroom.is_group ? chatroom.name : getChatName(chatroom));
    currentChatroom = chatroom;

    // 设置为初始加载状态
    initialLoadComplete[chatroom.id] = false;

    // 更新UI状态
    setCurrentChatName(chatroom.is_group ? chatroom.name : getChatName(chatroom));

    // 启用输入框和按钮
    elements.messageInput.disabled = false;
    elements.sendBtn.disabled = false;

    // 启用消息工具栏按钮
    if (elements.emojiBtn) elements.emojiBtn.disabled = false;
    if (elements.imageBtn) elements.imageBtn.disabled = false;
    if (elements.quickReplyBtn) elements.quickReplyBtn.disabled = false;

    // 如果消息工具模块已加载，调用其启用方法
    if (typeof window.enableMessageTools === 'function') {
        window.enableMessageTools(true);
    }

    // 输入框获取焦点
    elements.messageInput.focus();

    // 更新聊天列表UI激活状态
    updateChatListActiveState(chatroom.id);

    // 显示/隐藏添加用户按钮
    elements.addUserBtn.style.display = chatroom.is_group ? 'block' : 'none';

    // 显示/隐藏群组成员面板
    if (elements.groupMembersPanel) {
        elements.groupMembersPanel.style.display = chatroom.is_group ? 'flex' : 'none';
    }

    // 显示群成员信息 (如果是群聊)
    displayCurrentChatInfo(chatroom.id);

    // 获取消息历史
    getMessageHistory(chatroom.id);

    // 加载卡片确认状态
    loadCardConfirmations(chatroom.id).catch(error => {
        console.error('加载卡片确认状态失败:', error);
    });

    // 更新整体UI状态
    updateUIState(true, chatroom);
}

/**
 * 更新聊天列表UI激活状态
 * @param {string} activeChatroomId - 当前激活的聊天室ID
 */
function updateChatListActiveState(activeChatroomId) {
    // 移除所有聊天项的激活状态
    document.querySelectorAll('.chat-item').forEach(item => {
        item.classList.remove('active');
    });

    // 查找并激活当前聊天项
    const currentChatItem = document.querySelector(`.chat-item[data-id="${activeChatroomId}"]`);
    if (currentChatItem) {
        currentChatItem.classList.add('active');
        // 重置未读消息计数
        resetUnreadCount(activeChatroomId);
    } else {
        console.warn("无法在UI中找到聊天室项目，可能需要重新渲染聊天列表");
        renderChatList();
    }
}

/**
 * 开始私聊
 * @param {string} userId - 用户ID
 */
export function startChat(userId) {
    const currentUser = getCurrentUser();
    if (!currentUser) return;

    console.log("开始与用户聊天:", userId);

    // 先检查是否已经存在与该用户的聊天室
    const existingChat = findPrivateChatWithUser(userId);

    if (existingChat) {
        console.log("找到现有聊天室:", existingChat);
        selectChatroom(existingChat);
        return;
    }

    // 如果不存在，发送消息创建新的聊天室
    sendChatMessage(
        { is_group: false, members: [currentUser.id, userId] },
        '您好，我想和您聊天。',
        currentUser.id
    );

    // 添加轮询检查新聊天室的函数
    checkForNewPrivateChat(userId);
}

/**
 * 查找与指定用户的私聊
 * @param {string} userId - 用户ID
 * @returns {Object|null} 找到的聊天室或null
 */
function findPrivateChatWithUser(userId) {
    const currentUser = getCurrentUser();
    if (!currentUser) return null;

    return chatrooms.find(c =>
        !c.is_group &&
        c.members.includes(userId) &&
        c.members.includes(currentUser.id) &&
        c.members.length === 2
    );
}

/**
 * 轮询检查新的私聊聊天室
 * @param {string} targetUserId - 目标用户ID
 * @param {number} attempts - 已尝试次数
 */
export function checkForNewPrivateChat(targetUserId, attempts = 0) {
    const currentUser = getCurrentUser();
    if (!currentUser) return;

    // 最多尝试指定次数
    if (attempts >= MAX_POLLING_ATTEMPTS) {
        console.warn("无法找到与用户的新聊天室:", targetUserId);
        return;
    }

    // 查找与目标用户的私聊聊天室
    const chat = findPrivateChatWithUser(targetUserId);

    if (chat) {
        // 找到聊天室，选择它
        console.log("找到新的私聊聊天室:", chat);
        selectChatroom(chat);
    } else {
        // 继续尝试
        setTimeout(() => {
            checkForNewPrivateChat(targetUserId, attempts + 1);
        }, POLLING_INTERVAL);
    }
}

// =============== 消息操作函数 ===============

/**
 * 获取消息历史
 * @param {string} chatroomId - 聊天室ID
 * @param {number} offset - 偏移量
 * @param {boolean} keepScrollPosition - 是否保持滚动位置
 */
export function getMessageHistory(chatroomId, offset = 0, keepScrollPosition = false) {
    console.log("获取聊天室消息历史:", chatroomId, "偏移量:", offset, "保持滚动位置:", keepScrollPosition);

    // 重置消息偏移量
    if (offset === 0) {
        messageOffset[chatroomId] = 0;

        // 清空当前消息
        if (messages[chatroomId]) {
            messages[chatroomId] = [];
        }

        // 清空消息区域并显示加载状态
        showEmptyState('加载消息中...');
    }

    // 保存请求时的状态，用于后续处理
    if (keepScrollPosition) {
        // 存储当前滚动位置和内容高度
        elements.chatMessages._oldScrollTop = elements.chatMessages.scrollTop;
        elements.chatMessages._oldScrollHeight = elements.chatMessages.scrollHeight;
    }

    requestMessageHistory(chatroomId, offset, MESSAGE_LIMIT);
}

/**
 * 加载更多消息
 */
export function loadMoreMessages() {
    if (!currentChatroom) return;

    // 显示加载中提示
    addLoadingIndicator();

    const offset = messageOffset[currentChatroom.id] || 0;
    // 加载更多历史消息时保持滚动位置
    getMessageHistory(currentChatroom.id, offset, true);
}

/**
 * 处理历史消息
 * @param {Object} data - 消息数据对象
 */
export function handleHistoryMessages(data) {
    const historyMsgs = data.messages;
    const chatroomId = data.chatroom_id;
    const offset = data.offset;

    // 检查是否是加载更多消息（保持滚动位置）
    const isLoadingMore = offset > 0;
    const oldScrollTop = elements.chatMessages._oldScrollTop;
    const oldScrollHeight = elements.chatMessages._oldScrollHeight;

    if (!messages[chatroomId]) {
        messages[chatroomId] = [];
    }

    // 更新消息偏移量
    messageOffset[chatroomId] = offset + historyMsgs.length;

    // 添加消息
    historyMsgs.forEach(msg => {
        const exists = messages[chatroomId].some(m => m.id === msg.id);
        if (!exists) {
            messages[chatroomId].push(msg);
        }
    });

    // 按时间排序
    messages[chatroomId].sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

    // 如果是当前聊天室的消息，则渲染
    if (currentChatroom && currentChatroom.id === chatroomId) {
        renderMessages(isLoadingMore);

        // 如果是加载更多消息，恢复滚动位置
        if (isLoadingMore && oldScrollTop !== undefined && oldScrollHeight !== undefined) {
            // 强制浏览器重绘，确保高度计算正确
            void elements.chatMessages.offsetHeight;

            // 使用requestAnimationFrame确保在下一帧渲染时恢复滚动位置
            requestAnimationFrame(() => {
                // 计算新的滚动位置: 当前高度 - 原来高度 + 原来滚动位置
                const newScrollTop = elements.chatMessages.scrollHeight - oldScrollHeight + oldScrollTop;
                elements.chatMessages.scrollTop = newScrollTop;

                // 清除临时保存的滚动数据
                delete elements.chatMessages._oldScrollTop;
                delete elements.chatMessages._oldScrollHeight;
            });
        }

        // 判断是否需要显示加载更多按钮
        if (!initialLoadComplete[chatroomId]) {
            initialLoadComplete[chatroomId] = true;
            toggleLoadMoreButton(historyMsgs.length >= MESSAGE_LIMIT);
        } else {
            // 非初始加载，如果不足限制条数，说明没有更多历史消息了
            toggleLoadMoreButton(historyMsgs.length >= MESSAGE_LIMIT);
        }

        // 移除加载指示器
        removeLoadingIndicator();
    }
}

/**
 * 处理新消息
 * @param {Object} data - 消息数据对象
 */
export function handleNewMessage(data) {
    const msg = data.message;

    if (!messages[msg.chatroom_id]) {
        messages[msg.chatroom_id] = [];
    }

    // 检查是否消息已存在
    const exists = messages[msg.chatroom_id].some(m => m.id === msg.id);
    if (!exists) {
        // 添加到消息数组
        messages[msg.chatroom_id].push(msg);

        // 如果是当前聊天室的消息，则添加到聊天界面
        const isCurrentChatroom = currentChatroom && currentChatroom.id === msg.chatroom_id;
        if (isCurrentChatroom) {
            // 检查是否存在空状态提示，如果存在则清除
            const emptyState = elements.chatMessages.querySelector('.empty-state');
            if (emptyState) {
                clearMessages();
            }

            // 检测浏览器类型
            const userAgent = navigator.userAgent;
            const is360Browser = userAgent.indexOf('QIHU') > -1 || userAgent.indexOf('360Browser') > -1 || userAgent.indexOf('QHBrowser') > -1;

            // 只添加新消息，而不重新渲染所有消息
            const messageElement = createMessageElement(msg);
            elements.chatMessages.appendChild(messageElement);

            // 基本的重绘触发 - 对所有浏览器都适用且影响最小
            void elements.chatMessages.offsetHeight;

            // 添加淡入动画 - 提供良好的用户体验
            messageElement.style.animation = 'fadeIn 0.3s';

            // 使用requestAnimationFrame确保在下一帧渲染
            requestAnimationFrame(() => {
                // 滚动到最新消息
                scrollToLatestMessage();

                // 针对360浏览器的特殊处理
                if (is360Browser) {
                    console.log("检测到360浏览器，应用特殊渲染逻辑");

                    // 强制重绘方法1：修改样式
                    elements.chatMessages.style.opacity = '0.99';
                    void elements.chatMessages.offsetHeight;
                    elements.chatMessages.style.opacity = '1';

                    // 强制重绘方法2：使用transform
                    elements.chatMessages.style.transform = 'translateZ(0)';
                    void elements.chatMessages.offsetHeight;

                    // 强制重绘方法3：使用类
                    elements.chatMessages.classList.add('force-redraw');

                    // 使用setTimeout添加延迟，确保DOM已更新
                    setTimeout(() => {
                        // 强制重绘方法4：显示/隐藏
                        elements.chatMessages.style.display = 'none';
                        void elements.chatMessages.offsetHeight;
                        elements.chatMessages.style.display = 'flex';

                        // 强制重绘方法5：可见性
                        elements.chatMessages.style.visibility = 'hidden';
                        void elements.chatMessages.offsetHeight;
                        elements.chatMessages.style.visibility = 'visible';

                        // 移除强制重绘类
                        elements.chatMessages.classList.remove('force-redraw');

                        // 再次滚动到最新消息
                        scrollToLatestMessage();
                    }, 20);
                }
            });
        }

        // 更新聊天列表中的最后一条消息
        updateChatLastMessage(msg.chatroom_id, msg, isCurrentChatroom);
    }
}

/**
 * 更新聊天室最后一条消息并更新UI
 * @param {string} chatroomId - 聊天室ID
 * @param {Object} message - 消息对象
 * @param {boolean} isCurrentChatroom - 是否为当前选中的聊天室
 */
export function updateChatLastMessage(chatroomId, message, isCurrentChatroom = false) {
    const chatroom = chatrooms.find(c => c.id === chatroomId);
    if (chatroom) {
        // 更新聊天室的最后一条消息
        chatroom.last_message = message;

        // 重新排序聊天室列表，确保最新消息的聊天室在顶部
        chatrooms.sort((a, b) => {
            // 如果没有最后消息，则使用聊天室创建时间
            const timeA = a.last_message ? new Date(a.last_message.created_at) : new Date(a.created_at);
            const timeB = b.last_message ? new Date(b.last_message.created_at) : new Date(b.created_at);
            return timeB - timeA; // 降序排列，最新的在前面
        });

        // 更新UI并处理未读消息
        updateChatItemLastMessage(chatroomId, message, isCurrentChatroom);

        // 可能已经创建了新的聊天项，重新渲染列表
        if (!document.querySelector(`.chat-item[data-id="${chatroomId}"]`)) {
            renderChatList();
        }
    }
}

/**
 * 发送消息
 */
export function sendMessage() {
    const content = elements.messageInput.value.trim();
    if (!content || !currentChatroom) return;

    const success = sendChatMessage(currentChatroom, content, getCurrentUser().id);

    if (success) {
        elements.messageInput.value = '';
    }
}

/**
 * 编辑消息
 * @param {string} messageId - 消息ID
 * @param {string} newContent - 新消息内容
 */
export function editMessage(messageId, newContent) {
    editChatMessage(messageId, newContent, getCurrentUser().id);
}

// =============== 群组成员管理函数 ===============

/**
 * 获取当前选中的成员
 * @returns {Array} 选中的成员数组
 */
export function getSelectedMembers() {
    return selectedMembers;
}

/**
 * 设置选中的成员
 * @param {Array} members - 成员数组
 */
export function setSelectedMembers(members) {
    selectedMembers = members;
    updateSelectedMembers();
}

/**
 * 添加选中的成员
 * @param {Object} member - 成员对象
 */
export function addSelectedMember(member) {
    if (!selectedMembers.some(m => m.id === member.id)) {
        selectedMembers.push(member);
        updateSelectedMembers();
    }
}

/**
 * 移除选中的成员
 * @param {string} memberId - 成员ID
 */
export function removeSelectedMember(memberId) {
    selectedMembers = selectedMembers.filter(m => m.id !== memberId);
    updateSelectedMembers();
}

/**
 * 将用户添加到群组
 * @param {string} userId - 用户ID
 * @param {string} groupId - 群组ID
 */
export function addToGroup(userId, groupId) {
    const currentUser = getCurrentUser();
    if (!currentUser) return;

    addUserToGroup(userId, groupId, currentUser.id);
}