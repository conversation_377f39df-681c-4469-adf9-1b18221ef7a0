#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连接池测试脚本
测试数据库和Redis连接池的性能和功能
"""

import asyncio
import time
import statistics
from typing import List
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import get_db, engine
from utils.redis_util import get_redis_instance, get_redis_pool, close_redis_pool
from sqlalchemy.orm import Session
from sqlalchemy import text


class ConnectionPoolTester:
    """连接池测试类"""
    
    def __init__(self):
        self.db_times = []
        self.redis_times = []
    
    async def test_database_pool(self, iterations: int = 100):
        """测试数据库连接池性能"""
        print(f"\n🔍 测试数据库连接池性能 ({iterations}次操作)")
        print("-" * 50)
        
        start_time = time.time()
        
        for i in range(iterations):
            operation_start = time.time()
            
            # 获取数据库连接
            db = next(get_db())
            try:
                # 执行简单查询
                result = db.execute(text("SELECT 1 as test"))
                row = result.fetchone()
                assert row[0] == 1
            finally:
                db.close()
            
            operation_time = time.time() - operation_start
            self.db_times.append(operation_time * 1000)  # 转换为毫秒
            
            if (i + 1) % 20 == 0:
                print(f"已完成 {i + 1}/{iterations} 次数据库操作")
        
        total_time = time.time() - start_time
        
        # 统计结果
        avg_time = statistics.mean(self.db_times)
        min_time = min(self.db_times)
        max_time = max(self.db_times)
        median_time = statistics.median(self.db_times)
        
        print(f"\n📊 数据库连接池测试结果:")
        print(f"总耗时: {total_time:.2f}秒")
        print(f"平均响应时间: {avg_time:.2f}ms")
        print(f"最小响应时间: {min_time:.2f}ms")
        print(f"最大响应时间: {max_time:.2f}ms")
        print(f"中位数响应时间: {median_time:.2f}ms")
        print(f"QPS: {iterations/total_time:.2f}")
        
        # 连接池状态
        pool = engine.pool
        print(f"\n🔗 连接池状态:")
        print(f"连接池大小: {pool.size()}")
        print(f"当前连接数: {pool.checkedin()}")
        print(f"已检出连接数: {pool.checkedout()}")
        print(f"溢出连接数: {pool.overflow()}")
    
    async def test_redis_pool(self, iterations: int = 100):
        """测试Redis连接池性能"""
        print(f"\n🔍 测试Redis连接池性能 ({iterations}次操作)")
        print("-" * 50)
        
        start_time = time.time()
        
        for i in range(iterations):
            operation_start = time.time()
            
            # 获取Redis连接
            redis = await get_redis_instance()
            
            # 执行简单操作
            await redis.set(f"test_key_{i}", f"test_value_{i}")
            value = await redis.get(f"test_key_{i}")
            assert value == f"test_value_{i}"
            
            # 清理测试数据
            await redis.delete(f"test_key_{i}")
            
            operation_time = time.time() - operation_start
            self.redis_times.append(operation_time * 1000)  # 转换为毫秒
            
            if (i + 1) % 20 == 0:
                print(f"已完成 {i + 1}/{iterations} 次Redis操作")
        
        total_time = time.time() - start_time
        
        # 统计结果
        avg_time = statistics.mean(self.redis_times)
        min_time = min(self.redis_times)
        max_time = max(self.redis_times)
        median_time = statistics.median(self.redis_times)
        
        print(f"\n📊 Redis连接池测试结果:")
        print(f"总耗时: {total_time:.2f}秒")
        print(f"平均响应时间: {avg_time:.2f}ms")
        print(f"最小响应时间: {min_time:.2f}ms")
        print(f"最大响应时间: {max_time:.2f}ms")
        print(f"中位数响应时间: {median_time:.2f}ms")
        print(f"QPS: {iterations/total_time:.2f}")
        
        # Redis连接池状态
        pool = await get_redis_pool()
        print(f"\n🔗 Redis连接池状态:")
        print(f"最大连接数: {pool.max_connections}")
        print(f"当前连接数: {len(pool._available_connections) + len(pool._in_use_connections)}")
        print(f"可用连接数: {len(pool._available_connections)}")
        print(f"使用中连接数: {len(pool._in_use_connections)}")
    
    async def test_concurrent_operations(self, concurrent_count: int = 50):
        """测试并发操作"""
        print(f"\n🔍 测试并发操作 ({concurrent_count}个并发)")
        print("-" * 50)
        
        async def db_operation():
            """数据库并发操作"""
            db = next(get_db())
            try:
                result = db.execute(text("SELECT SLEEP(0.1), 1 as test"))
                row = result.fetchone()
                return row[1]
            finally:
                db.close()
        
        async def redis_operation():
            """Redis并发操作"""
            redis = await get_redis_instance()
            key = f"concurrent_test_{time.time()}"
            await redis.set(key, "test_value", ex=10)
            value = await redis.get(key)
            await redis.delete(key)
            return value
        
        # 测试数据库并发
        start_time = time.time()
        db_tasks = [asyncio.create_task(asyncio.to_thread(db_operation)) 
                   for _ in range(concurrent_count)]
        db_results = await asyncio.gather(*db_tasks)
        db_time = time.time() - start_time
        
        # 测试Redis并发
        start_time = time.time()
        redis_tasks = [redis_operation() for _ in range(concurrent_count)]
        redis_results = await asyncio.gather(*redis_tasks)
        redis_time = time.time() - start_time
        
        print(f"📊 并发测试结果:")
        print(f"数据库并发 ({concurrent_count}个): {db_time:.2f}秒")
        print(f"Redis并发 ({concurrent_count}个): {redis_time:.2f}秒")
        print(f"数据库成功率: {len([r for r in db_results if r == 1])/len(db_results)*100:.1f}%")
        print(f"Redis成功率: {len([r for r in redis_results if r == b'test_value'])/len(redis_results)*100:.1f}%")
    
    def print_summary(self):
        """打印测试总结"""
        print(f"\n" + "="*60)
        print(f"🎯 连接池测试总结")
        print(f"="*60)
        
        if self.db_times:
            db_avg = statistics.mean(self.db_times)
            print(f"数据库平均响应时间: {db_avg:.2f}ms")
        
        if self.redis_times:
            redis_avg = statistics.mean(self.redis_times)
            print(f"Redis平均响应时间: {redis_avg:.2f}ms")
        
        print(f"\n✅ 连接池配置建议:")
        print(f"- 数据库连接池大小: 20-50 (根据并发量调整)")
        print(f"- Redis连接池大小: 50-100 (根据使用频率调整)")
        print(f"- 启用连接健康检查和超时设置")
        print(f"- 定期监控连接池使用情况")


async def main():
    """主测试函数"""
    print("🚀 开始连接池性能测试")
    print("="*60)
    
    tester = ConnectionPoolTester()
    
    try:
        # 测试数据库连接池
        await tester.test_database_pool(100)
        
        # 测试Redis连接池
        await tester.test_redis_pool(100)
        
        # 测试并发操作
        await tester.test_concurrent_operations(20)
        
        # 打印总结
        tester.print_summary()
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        try:
            await close_redis_pool()
            print(f"\n🧹 Redis连接池已关闭")
        except Exception as e:
            print(f"关闭Redis连接池时出错: {e}")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
