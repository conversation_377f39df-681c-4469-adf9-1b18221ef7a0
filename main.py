import uvicorn
from fastapi import FastAPI, Depends
import asyncio
from redis import asyncio as aioredis
import json
import os
from app.chat_manager import chat
from config.get_config import config
from app.database import engine, get_db
from app.models import Base
from sqlalchemy.orm import Session
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse

# redis配置
url = (config['db_redis']['host'], config['db_redis']['port'])
host = config['db_redis']['host']
db = config['db_redis']['db']
timeout = config['db_redis']['timeout']
password = config['db_redis']['password']
port = config['db_redis']['port']

# 初始化数据库
Base.metadata.create_all(bind=engine)


# 初始化app
app = FastAPI(title="Ws Chat", description="聊天应用", version="1.0.0")
app.openapi_version = "3.0.0"

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

# 导入API模块
from app.api import upload, trtc, card

# 挂载API路由
app.include_router(chat.app, prefix='/api/chat', tags=['Chat'])
app.include_router(upload.router, prefix='/api/upload', tags=['Upload'])
app.include_router(trtc.router, prefix='/api/trtc', tags=['trtc'])
app.include_router(card.router, prefix='/api', tags=['Card'])

# 根路径重定向到前端页面
@app.get("/")
async def read_root():
    return FileResponse('static/index.html')


# 保存后台任务的引用，以便在关闭时取消
background_tasks = []

@app.on_event('startup')
async def on_startup():
    print(f"订阅初始化:{os.getpid()}")

    # 启动WebSocket心跳检查任务
    try:
        print("启动WebSocket心跳检查任务...")
        await chat.cm.start_heartbeat_check()
        print("WebSocket心跳检查任务已启动")
    except Exception as e:
        print(f"启动WebSocket心跳检查任务时出错: {e}")

    # 执行消息订阅机制
    loop = asyncio.get_event_loop()
    task = loop.create_task(register_pubsub())
    # 保存任务引用
    background_tasks.append(task)

@app.on_event('shutdown')
async def on_shutdown():
    print("应用程序关闭中，取消后台任务...")

    # 停止心跳检查任务
    try:
        print("停止WebSocket心跳检查任务...")
        await chat.cm.stop_heartbeat_check()
        print("WebSocket心跳检查任务已停止")
    except Exception as e:
        print(f"停止WebSocket心跳检查任务时出错: {e}")

    # 断开所有WebSocket连接
    try:
        print("断开所有WebSocket连接...")
        for user_id in list(chat.cm.websocket_connections.keys()):
            try:
                await chat.cm.disconnect(user_id)
            except Exception as e:
                print(f"断开用户 {user_id} 连接时出错: {e}")
        print("所有WebSocket连接已断开")
    except Exception as e:
        print(f"断开WebSocket连接时出错: {e}")

    # 取消所有后台任务
    for task in background_tasks:
        if not task.done():
            task.cancel()
            try:
                # 等待任务取消完成
                await asyncio.wait_for(task, timeout=5.0)
            except asyncio.TimeoutError:
                print(f"任务取消超时: {task}")
            except asyncio.CancelledError:
                print(f"任务已取消: {task}")
            except Exception as e:
                print(f"取消任务时出错: {e}")

    # 关闭Redis连接池
    try:
        from utils.redis_util import close_redis_pool
        await close_redis_pool()
        print("Redis连接池已关闭")
    except Exception as e:
        print(f"关闭Redis连接池时出错: {e}")

    print("应用程序关闭完成")


async def reader(channel):
    """处理Redis频道消息

    Args:
        channel: Redis PubSub频道
    """
    try:
        # 进行消息的消费
        async for msg in channel.listen():
            # 检查是否被取消
            if asyncio.current_task().cancelled():
                print("Redis消息监听任务被取消")
                break

            # 处理消息
            msg_data = msg.get("data")
            if msg_data and isinstance(msg_data, str):
                try:
                    msg_data_dict = json.loads(msg_data)
                    print(f"chat:{msg_data_dict}")
                    sender = msg_data_dict.get("sender")

                    # 使用数据库会话
                    async with asyncio.Lock():
                        db = None
                        try:
                            # 获取新的数据库会话
                            db = next(get_db())

                            # 进行消息处理
                            await chat.cm.handle_websocket_message(msg_data_dict, sender, db)
                        except Exception as e:
                            print(f"处理Redis消息错误: {str(e)}, 消息类型: {msg_data_dict.get('type', 'unknown')}")
                            # 尝试回滚事务
                            if db:
                                try:
                                    db.rollback()
                                except Exception as rollback_error:
                                    print(f"回滚数据库事务错误: {str(rollback_error)}")
                        finally:
                            # 确保数据库会话被关闭
                            if db:
                                try:
                                    db.close()
                                except Exception as close_error:
                                    print(f"关闭数据库会话错误: {str(close_error)}")
                except json.JSONDecodeError as json_error:
                    print(f"解析Redis消息错误: {str(json_error)}, 消息内容: {msg_data}")
                except Exception as e:
                    print(f"处理Redis消息未知错误: {str(e)}")
    except asyncio.CancelledError:
        # 优雅地处理取消操作
        print("Redis消息监听任务被取消")
        raise
    except Exception as e:
        print(f"Redis消息监听任务异常: {str(e)}")
        raise


async def register_pubsub():
    """注册Redis PubSub并处理消息"""
    from utils.redis_util import create_redis_client

    # 创建Redis连接
    redis_client = None
    psub = None

    try:
        # 使用优化的Redis客户端创建函数
        redis_client = await create_redis_client()

        # 创建PubSub对象
        psub = redis_client.pubsub()

        # 订阅频道并处理消息
        async with psub as p:
            try:
                # 消息订阅
                await p.subscribe("chat")
                print("已成功订阅Redis频道: chat")

                # 处理消息
                await reader(p)
            except asyncio.CancelledError:
                print("Redis PubSub任务被取消")
                raise
            except Exception as e:
                print(f"Redis订阅错误: {str(e)}")
            finally:
                # 确保取消订阅
                try:
                    if p.subscribed:
                        await p.unsubscribe("chat")
                        print("已取消订阅Redis频道: chat")
                except Exception as e:
                    print(f"取消Redis订阅错误: {str(e)}")
    except asyncio.CancelledError:
        print("Redis PubSub任务被取消")
        raise
    except Exception as e:
        print(f"Redis PubSub任务异常: {str(e)}")
        raise
    finally:
        # 确保关闭连接
        if psub:
            try:
                await psub.close()
            except Exception as e:
                print(f"关闭Redis PubSub连接错误: {str(e)}")

        if redis_client:
            try:
                await redis_client.close()
            except Exception as e:
                print(f"关闭Redis客户端错误: {str(e)}")


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=298)
    #运行命令：
    # uvicorn main:app --reload  默认127.0.0.1  局域网不能访问
    # uvicorn main:app --reload --host 0.0.0.0 --port 298
